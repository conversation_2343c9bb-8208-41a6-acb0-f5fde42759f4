// LinkedIn Profile Automation Handler
class LinkedInProfileAutomation {
    constructor() {
        this.isProcessing = false;
        this.connectionData = null;
        this.maxRetries = 5;
        this.retryDelay = 2000;
        this.init();
    }

    init() {
        // Check if we're on a LinkedIn profile page
        if (this.isLinkedInProfilePage()) {
            this.checkForPendingConnection();
        }
    }

    isLinkedInProfilePage() {
        const url = window.location.href;
        return url.includes('linkedin.com/in/') && !url.includes('/search');
    }

    checkForPendingConnection() {
        // Check if there's a pending connection request
        const pendingData = sessionStorage.getItem('pendingConnection');
        if (pendingData) {
            try {
                this.connectionData = JSON.parse(pendingData);
                // Clear the data to prevent duplicate processing
                sessionStorage.removeItem('pendingConnection');
                
                // Wait for page to fully load
                setTimeout(() => {
                    this.processConnection();
                }, 3000);
            } catch (error) {
                console.error('Error parsing pending connection data:', error);
                this.sendResult(false, 'Invalid connection data');
            }
        }
    }

    async processConnection() {
        if (this.isProcessing) return;
        this.isProcessing = true;

        try {
            // Step 1: Find and click Connect button
            const connectResult = await this.findAndClickConnectButton();
            if (!connectResult.success) {
                this.sendResult(false, connectResult.error);
                return;
            }

            // Step 2: Wait for popup and handle it
            const popupResult = await this.handleConnectionPopup();
            if (!popupResult.success) {
                this.sendResult(false, popupResult.error);
                return;
            }

            // Step 3: Success
            this.sendResult(true, 'Connection request sent successfully');

        } catch (error) {
            console.error('Error processing connection:', error);
            this.sendResult(false, error.message);
        } finally {
            this.isProcessing = false;
        }
    }

    async findAndClickConnectButton() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = this.maxRetries;

            const findButton = () => {
                attempts++;
                
                // Multiple selectors for Connect button
                const selectors = [
                    'button[aria-label*="Connect"]',
                    'button[data-control-name="connect"]',
                    'button:contains("Connect")',
                    '.pv-s-profile-actions button[aria-label*="Connect"]',
                    '.pvs-profile-actions button[aria-label*="Connect"]',
                    'button[data-test-app-aware-link]'
                ];

                let connectButton = null;

                // Try each selector
                for (const selector of selectors) {
                    if (selector.includes(':contains')) {
                        // Handle :contains selector manually
                        const buttons = document.querySelectorAll('button');
                        for (const button of buttons) {
                            if (button.textContent.trim().toLowerCase().includes('connect')) {
                                connectButton = button;
                                break;
                            }
                        }
                    } else {
                        connectButton = document.querySelector(selector);
                    }
                    
                    if (connectButton && this.isValidConnectButton(connectButton)) {
                        break;
                    }
                }

                if (connectButton && this.isValidConnectButton(connectButton)) {
                    console.log('Found Connect button:', connectButton);
                    connectButton.click();
                    resolve({ success: true });
                    return;
                }

                if (attempts < maxAttempts) {
                    setTimeout(findButton, this.retryDelay);
                } else {
                    resolve({ success: false, error: 'Connect button not found after multiple attempts' });
                }
            };

            findButton();
        });
    }

    isValidConnectButton(button) {
        if (!button || button.disabled) return false;
        
        const text = button.textContent.trim().toLowerCase();
        const ariaLabel = button.getAttribute('aria-label')?.toLowerCase() || '';
        
        // Check if it's actually a Connect button
        const isConnect = text.includes('connect') || ariaLabel.includes('connect');
        
        // Exclude buttons that are not the main connect action
        const isExcluded = text.includes('following') || 
                          text.includes('message') || 
                          text.includes('more') ||
                          ariaLabel.includes('following') ||
                          ariaLabel.includes('message');
        
        return isConnect && !isExcluded;
    }

    async handleConnectionPopup() {
        return new Promise((resolve) => {
            let attempts = 0;
            const maxAttempts = this.maxRetries;

            const handlePopup = () => {
                attempts++;

                // Look for the connection popup/modal
                const popupSelectors = [
                    '[data-test-modal]',
                    '.artdeco-modal',
                    '.send-invite',
                    '[role="dialog"]'
                ];

                let popup = null;
                for (const selector of popupSelectors) {
                    popup = document.querySelector(selector);
                    if (popup && popup.offsetParent !== null) { // Check if visible
                        break;
                    }
                }

                if (popup) {
                    console.log('Found connection popup:', popup);
                    this.fillConnectionMessage(popup, resolve);
                    return;
                }

                if (attempts < maxAttempts) {
                    setTimeout(handlePopup, 1000);
                } else {
                    resolve({ success: false, error: 'Connection popup not found' });
                }
            };

            // Start looking for popup after a short delay
            setTimeout(handlePopup, 1000);
        });
    }

    fillConnectionMessage(popup, resolve) {
        try {
            // Look for message textarea
            const messageSelectors = [
                'textarea[name="message"]',
                'textarea[id*="custom-message"]',
                '.send-invite__custom-message textarea',
                'textarea'
            ];

            let messageTextarea = null;
            for (const selector of messageSelectors) {
                messageTextarea = popup.querySelector(selector);
                if (messageTextarea) break;
            }

            if (messageTextarea && this.connectionData.message) {
                // Clear existing text and type new message
                messageTextarea.value = '';
                messageTextarea.focus();
                
                // Type message character by character to simulate human typing
                this.typeMessage(messageTextarea, this.connectionData.message);
            }

            // Find and click Send button
            setTimeout(() => {
                const sendButton = this.findSendButton(popup);
                if (sendButton) {
                    sendButton.click();
                    resolve({ success: true });
                } else {
                    resolve({ success: false, error: 'Send button not found' });
                }
            }, 2000);

        } catch (error) {
            resolve({ success: false, error: `Error filling message: ${error.message}` });
        }
    }

    typeMessage(textarea, message) {
        let index = 0;
        const typeChar = () => {
            if (index < message.length) {
                textarea.value += message[index];
                // Trigger input event
                textarea.dispatchEvent(new Event('input', { bubbles: true }));
                index++;
                setTimeout(typeChar, 50 + Math.random() * 50); // Random delay between 50-100ms
            }
        };
        typeChar();
    }

    findSendButton(popup) {
        const sendSelectors = [
            'button[aria-label*="Send"]',
            'button[data-control-name="send_invite"]',
            'button:contains("Send")',
            '.send-invite__actions button[type="submit"]',
            'button[type="submit"]'
        ];

        for (const selector of sendSelectors) {
            if (selector.includes(':contains')) {
                const buttons = popup.querySelectorAll('button');
                for (const button of buttons) {
                    if (button.textContent.trim().toLowerCase().includes('send')) {
                        return button;
                    }
                }
            } else {
                const button = popup.querySelector(selector);
                if (button && !button.disabled) {
                    return button;
                }
            }
        }
        return null;
    }

    sendResult(success, message) {
        // Send result back to parent window
        if (window.opener) {
            window.opener.postMessage({
                type: 'CONNECTION_RESULT',
                success: success,
                error: success ? null : message,
                message: message,
                profileUrl: window.location.href,
                timestamp: Date.now()
            }, '*');
        }

        // Close the tab after a short delay
        setTimeout(() => {
            window.close();
        }, 2000);
    }
}

// Initialize when page loads
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', () => {
        new LinkedInProfileAutomation();
    });
} else {
    new LinkedInProfileAutomation();
}

// Also initialize on navigation changes (for SPA behavior)
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        setTimeout(() => {
            new LinkedInProfileAutomation();
        }, 1000);
    }
}).observe(document, { subtree: true, childList: true });
