class LinkedInSearchFloatingUI {
    constructor() {
        this.ui = null;
        this.isCollecting = false;
        this.collectedProfiles = [];
        this.config = window.LinkedInSearchConfig || {};
        this.collectionInterval = null;
        this.observer = null;
        this.init();
    }

    async init() {
        await this.loadDependencies();
        this.injectCSS();
        await this.loadHTMLTemplate();
        this.setupEventListeners();
        this.showUI();
    }

    async loadDependencies() {
        if (!window.LinkedInSearchConfig) {
            await this.loadScript('content/linkedin-search-config.js');
            this.config = window.LinkedInSearchConfig || {};
        }
    }

    loadScript(src) {
        return new Promise((resolve, reject) => {
            if (document.querySelector(`script[src*="${src}"]`)) {
                resolve();
                return;
            }

            const script = document.createElement('script');
            script.src = chrome.runtime.getURL(src);
            script.onload = resolve;
            script.onerror = reject;
            document.head.appendChild(script);
        });
    }

    injectCSS() {
        if (document.getElementById('linkedin-search-ui-styles')) return;
        const link = document.createElement('link');
        link.id = 'linkedin-search-ui-styles';
        link.rel = 'stylesheet';
        link.href = chrome.runtime.getURL('content/linkedin-search-ui.css');
        link.onerror = () => {
            console.warn('Failed to load external CSS, falling back to inline styles');
            this.injectInlineCSS();
        };
        document.head.appendChild(link);
    }

    injectInlineCSS() {
        const style = document.createElement('style');
        style.id = 'linkedin-search-ui-inline-styles';
        style.textContent = `
            .linkedin-search-floating-ui {
                position: fixed !important;
                top: 80px !important;
                right: 20px !important;
                width: 420px !important;
                background: white !important;
                border-radius: 12px !important;
                box-shadow: 0 8px 32px rgba(0,0,0,0.15) !important;
                z-index: 999999 !important;
                display: flex !important;
                flex-direction: column !important;
            }
            .linkedin-search-header {
                background: linear-gradient(135deg, #0a66c2, #004182) !important;
                color: white !important;
                padding: 16px 20px !important;
                display: flex !important;
                justify-content: space-between !important;
                align-items: center !important;
            }
            .linkedin-search-btn {
                padding: 12px 16px !important;
                border: none !important;
                border-radius: 8px !important;
                font-weight: 600 !important;
                cursor: pointer !important;
            }
        `;
        document.head.appendChild(style);
    }

    async loadHTMLTemplate() {
        try {
            const response = await fetch(chrome.runtime.getURL('content/linkedin-search-ui.html'));
            const htmlContent = await response.text();
            const tempDiv = document.createElement('div');
            tempDiv.innerHTML = htmlContent;
            this.ui = tempDiv.firstElementChild;
            this.applyConfigurationToTemplate();

            document.body.appendChild(this.ui);

        } catch (error) {
            console.warn('Failed to load HTML template, using fallback:', error);
            this.createFallbackUI();
        }
    }

    applyConfigurationToTemplate() {
        if (!this.ui || !this.config) return;
        const elementsWithDataText = this.ui.querySelectorAll('[data-text]');

        elementsWithDataText.forEach(element => {
            const configPath = element.getAttribute('data-text');
            const configValue = this.getConfigValue(configPath);

            if (configValue) {
                if (element.id === 'start-connecting-btn') {
                    element.textContent = `${configValue} (0)`;
                } else if (element.classList.contains('profiles-count')) {
                    element.innerHTML = `${configValue} <span id="profile-count">0</span>`;
                } else {
                    element.textContent = configValue;
                }
            }
        });
    }

    getConfigValue(path) {
        const keys = path.split('.');
        let value = this.config;

        for (const key of keys) {
            if (value && typeof value === 'object' && key in value) {
                value = value[key];
            } else {
                return null;
            }
        }

        return value;
    }

    createFallbackUI() {
        console.error('Failed to load external HTML template. Creating minimal fallback UI.');

        this.ui = document.createElement('div');
        this.ui.className = 'linkedin-search-floating-ui';
        this.ui.innerHTML = `
            <div class="linkedin-search-header">
                <h3 class="linkedin-search-title">LinkedIn Search</h3>
                <button class="linkedin-search-close" title="Close">&times;</button>
            </div>
            <div class="linkedin-search-content">
                <p style="text-align: center; color: #dc3545; padding: 20px;">
                    Failed to load UI template. Please refresh the page.
                </p>
                <button class="linkedin-search-btn start" onclick="window.location.reload()">
                    Refresh Page
                </button>
            </div>
        `;
        document.body.appendChild(this.ui);

        this.ui.querySelector('.linkedin-search-close').addEventListener('click', () => {
            this.closeUI();
        });
    }

    setupEventListeners() {
        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        const connectBtn = this.ui.querySelector('#start-connecting-btn');
        const clearBtn = this.ui.querySelector('#clear-profiles');
        const closeBtn = this.ui.querySelector('.linkedin-search-close');
        const minimizeBtn = this.ui.querySelector('.linkedin-search-minimize');
        const header = this.ui.querySelector('.linkedin-search-header');

        collectBtn.addEventListener('click', () => this.toggleCollecting());
        connectBtn.addEventListener('click', () => this.startConnecting());
        clearBtn.addEventListener('click', () => this.clearProfiles());
        closeBtn.addEventListener('click', () => this.closeUI());
        minimizeBtn.addEventListener('click', () => this.toggleMinimize());

        this.makeDraggable(header);
    }

    makeDraggable(handle) {
        let isDragging = false;
        let currentX, currentY, initialX, initialY;
        let xOffset = 0, yOffset = 0;

        handle.addEventListener('mousedown', (e) => {
            initialX = e.clientX - xOffset;
            initialY = e.clientY - yOffset;
            if (e.target === handle || handle.contains(e.target)) {
                isDragging = true;
                this.ui.style.cursor = 'grabbing';
            }
        });

        document.addEventListener('mousemove', (e) => {
            if (isDragging) {
                e.preventDefault();
                currentX = e.clientX - initialX;
                currentY = e.clientY - initialY;
                xOffset = currentX;
                yOffset = currentY;
                this.ui.style.transform = `translate(${currentX}px, ${currentY}px)`;
            }
        });

        document.addEventListener('mouseup', () => {
            initialX = currentX;
            initialY = currentY;
            isDragging = false;
            this.ui.style.cursor = 'move';
        });
    }



    toggleCollecting() {
        if (this.isCollecting) {
            this.pauseCollecting();
        } else {
            this.startCollecting();
        }
    }

    startCollecting() {
        this.updateStatus('status', this.config.messages?.status?.collecting || 'Collecting profiles...', true);
        this.isCollecting = true;

        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        collectBtn.textContent = 'PAUSE COLLECTING';
        collectBtn.classList.remove('start');
        collectBtn.classList.add('pause');

        this.setupProfileObserver();
        this.collectCurrentPageProfiles();
        this.collectionInterval = setInterval(() => {
            this.collectCurrentPageProfiles();
        }, 3000);
    }

    pauseCollecting() {
        this.updateStatus('status', 'Collection paused. Click "START COLLECTING" to resume.', false);
        this.isCollecting = false;

        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }

        if (this.collectionInterval) {
            clearInterval(this.collectionInterval);
            this.collectionInterval = null;
        }

        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        collectBtn.textContent = 'START COLLECTING';
        collectBtn.classList.remove('pause');
        collectBtn.classList.add('start');
    }

    setupProfileObserver() {
        if (this.observer) return;
        this.observer = new MutationObserver((mutations) => {
            if (!this.isCollecting) return;
            let hasNewProfiles = false;
            mutations.forEach((mutation) => {
                mutation.addedNodes.forEach((node) => {
                    if (node.nodeType === 1 && this.isProfileElement(node)) {
                        hasNewProfiles = true;
                    }
                });
            });
            if (hasNewProfiles) {
                setTimeout(() => this.collectCurrentPageProfiles(), 500);
            }
        });
        this.observer.observe(document.body, { childList: true, subtree: true });
    }

    isProfileElement(element) {
        const selectors = ['.entity-result', '[data-chameleon-result-urn]', '.reusable-search__result-container', '.search-results__result-item', '[componentkey]'];
        return selectors.some(selector =>
            element.matches && element.matches(selector) ||
            element.querySelector && element.querySelector(selector)
        );
    }

    collectCurrentPageProfiles() {
        if (!this.isCollecting) return;
        const selectors = ['.entity-result', '[data-chameleon-result-urn]', '.reusable-search__result-container', '.search-results__result-item', 'div[componentkey]:has(a[href*="/in/"])', 'div:has(> div > figure img[alt]) div:has(a[href*="/in/"])'];
        let profileElements = [];
        for (const selector of selectors) {
            try {
                profileElements = document.querySelectorAll(selector);
                if (profileElements.length > 0) break;
            } catch (e) {
                continue;
            }
        }
        profileElements.forEach(element => {
            const profile = this.extractProfileData(element);
            if (profile && !this.isDuplicateProfile(profile)) {
                this.addProfile(profile);
            }
        });
    }

    extractProfileData(element) {
        try {
            const nameElement = element.querySelector('a[href*="/in/"][data-view-name="search-result-lockup-title"]') ||
                               element.querySelector('.entity-result__title-text a') ||
                               element.querySelector('.actor-name a') ||
                               element.querySelector('a[href*="/in/"]');
            if (!nameElement) return null;

            let name = nameElement.textContent?.trim();
            if (name && name.includes(' is reachable')) {
                name = name.replace(' is reachable', '').trim();
            }

            const url = nameElement.href.startsWith('http') ? nameElement.href : `https://www.linkedin.com${nameElement.getAttribute('href')}`;
            let title = '', company = '', location = '';
            const parentContainer = nameElement.closest('div[componentkey]') || element;
            const textElements = parentContainer.querySelectorAll('p');

            textElements.forEach((p) => {
                const text = p.textContent?.trim();
                if (!text || text.includes(name)) return;
                if (!title && text && !text.includes('•') && !text.includes(',')) {
                    title = text;
                } else if (!location && text && (text.includes(',') || text.includes('India') || text.includes('USA') || text.includes('UK'))) {
                    location = text;
                }
            });

            if (title && title.includes(' at ')) {
                const parts = title.split(' at ');
                title = parts[0]?.trim() || '';
                company = parts[1]?.trim() || '';
            }

            const imageElement = parentContainer.querySelector('img[alt="' + name + '"]') ||
                               parentContainer.querySelector('img[src*="profile"]') ||
                               parentContainer.querySelector('figure img');
            const profilePic = imageElement?.src || '';

            return { name, url, title, company, location, profilePic, timestamp: Date.now(), source: 'linkedin-search' };
        } catch (error) {
            console.error('Error extracting profile data:', error);
            return null;
        }
    }

    isDuplicateProfile(newProfile) {
        return this.collectedProfiles.some(profile =>
            profile.url === newProfile.url ||
            (profile.name === newProfile.name && profile.title === newProfile.title)
        );
    }

    addProfile(profile) {
        this.collectedProfiles.push(profile);
        this.updateProfilesList();
        this.updateProfileCount();
        this.showNextButton();
    }



    updateProfileCount() {
        const countElement = this.ui.querySelector('#profile-count');
        countElement.textContent = this.collectedProfiles.length;

        const nextBtn = this.ui.querySelector('#start-connecting-btn');
        nextBtn.textContent = `Next: Start Connecting (${this.collectedProfiles.length})`;
    }

    updateProfilesList() {
        const profilesList = this.ui.querySelector('#profiles-list');
        const emptyMessage = this.config.messages?.empty?.profiles || 'No profiles collected yet. Click "START COLLECTING" to begin.';

        if (this.collectedProfiles.length === 0) {
            profilesList.innerHTML = `<div class="empty-profiles">${emptyMessage}</div>`;
        } else {
            profilesList.innerHTML = this.collectedProfiles.map((profile, index) => `
                <div class="profile-item" data-profile-index="${index}">
                    <div class="profile-image">
                        ${profile.profilePic ?
                            `<img src="${profile.profilePic}" alt="${profile.name}" style="width: 40px; height: 40px; border-radius: 50%; object-fit: cover;">` :
                            `<div class="profile-initial">${profile.name ? profile.name.charAt(0).toUpperCase() : '?'}</div>`
                        }
                    </div>
                    <div class="profile-info">
                        <div class="profile-name" title="${profile.name}">${profile.name}</div>
                        <div class="profile-title" title="${profile.title}">${profile.title}</div>
                        ${profile.company ? `<div class="profile-company" title="${profile.company}">${profile.company}</div>` : ''}
                        <div class="profile-url" title="${profile.url}" data-url="${profile.url}" style="cursor: pointer;">${this.shortenUrl(profile.url)}</div>
                    </div>
                    <div class="profile-actions">
                        <button class="profile-action-btn remove-profile-btn" data-url="${profile.url}" title="Remove">✕</button>
                    </div>
                </div>
            `).join('');

            // Add event listeners for profile actions
            profilesList.querySelectorAll('.profile-url').forEach(urlElement => {
                urlElement.addEventListener('click', (e) => {
                    const url = e.target.getAttribute('data-url');
                    this.copyProfileUrl(url);
                });
            });

            profilesList.querySelectorAll('.remove-profile-btn').forEach(removeBtn => {
                removeBtn.addEventListener('click', (e) => {
                    const url = e.target.getAttribute('data-url');
                    this.removeProfile(url);
                });
            });
        }
    }

    shortenUrl(url) {
        // Extract LinkedIn profile ID for cleaner display
        const match = url.match(/\/in\/([^\/\?]+)/);
        if (match) return `linkedin.com/in/${match[1]}`;
        return url.length > 30 ? url.substring(0, 30) + '...' : url;
    }

    copyProfileUrl(url) {
        navigator.clipboard.writeText(url).then(() => {
            const notification = document.createElement('div');
            notification.style.cssText = `position: fixed; top: 20px; right: 20px; background: #28a745; color: white; padding: 10px 15px; border-radius: 5px; z-index: 10001; font-size: 12px;`;
            notification.textContent = 'Profile URL copied!';
            document.body.appendChild(notification);
            setTimeout(() => notification.remove(), 2000);
        }).catch(err => console.error('Failed to copy URL:', err));
    }

    removeProfile(url) {
        this.collectedProfiles = this.collectedProfiles.filter(profile => profile.url !== url);
        this.updateProfilesList();
        this.updateProfileCount();
    }

    showNextButton() {
        const nextBtn = this.ui.querySelector('#start-connecting-btn');
        const buttonText = this.config.messages?.buttons?.startConnecting || 'Next: Start Connecting';

        nextBtn.style.display = 'block';
        nextBtn.disabled = this.collectedProfiles.length === 0;
        nextBtn.textContent = `${buttonText} (${this.collectedProfiles.length})`;

        const statusMessage = `${this.config.messages?.status?.collected || 'profiles collected. Ready to connect!'}`;
        this.updateStatus('status', `Collected ${this.collectedProfiles.length} ${statusMessage}`, false);
    }

    startConnecting() {
        // Check if automation UI is already open
        if (document.querySelector('.automation-starter-ui')) {
            console.log('Automation UI already open');
            return;
        }

        this.showAutomationStarterUI();
    }

    showAutomationStarterUI() {
        // Remove any existing automation UI first
        const existingAutomationUI = document.querySelector('.automation-starter-ui');
        if (existingAutomationUI) {
            existingAutomationUI.remove();
        }

        // Create automation starter UI
        const automationUI = this.createAutomationUI();
        document.body.appendChild(automationUI);

        // Hide the main search UI completely
        if (this.ui) {
            this.ui.style.display = 'none';
            this.ui.style.visibility = 'hidden';
        }

        // Initialize automation state
        this.automationState = {
            currentProfileIndex: 0,
            totalProfiles: this.collectedProfiles.length,
            isRunning: false,
            customPrompt: '',
            promptSet: false
        };

        this.updateAutomationProgress();
    }

    createAutomationUI() {
        const automationUI = document.createElement('div');
        automationUI.className = 'automation-starter-ui';
        automationUI.innerHTML = `
            <div class="automation-header">
                <h3>Processing Profiles</h3>
                <button class="automation-close" title="Close">&times;</button>
            </div>
            <div class="automation-content">
                <div class="progress-section">
                    <div class="progress-text">Progress: <span id="automation-progress">0 / ${this.collectedProfiles.length}</span></div>
                    <div class="progress-bar">
                        <div class="progress-fill" id="progress-fill" style="width: 0%"></div>
                    </div>
                </div>

                <div class="status-section">
                    <div class="current-status">Current Status: <span id="current-status">Ready to start</span></div>
                </div>

                <div class="stats-section">
                    <div class="stat-item">
                        <span class="stat-label">Profile Count:</span>
                        <span class="stat-value" id="total-profiles">${this.collectedProfiles.length}</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Successful:</span>
                        <span class="stat-value success" id="success-count">0</span>
                    </div>
                    <div class="stat-item">
                        <span class="stat-label">Failed:</span>
                        <span class="stat-value failed" id="failed-count">0</span>
                    </div>
                </div>

                <div class="current-profile-section">
                    <div class="current-profile-label">Current LinkedIn Profile URL:</div>
                    <div class="current-profile-url" id="current-profile-url">${this.collectedProfiles[0]?.url || 'None selected'}</div>
                </div>

                <div class="prompt-section" id="prompt-section">
                    <label for="custom-prompt">Custom Prompt:</label>
                    <textarea id="custom-prompt" placeholder="Enter your custom prompt for message generation..." rows="4"></textarea>
                    <button id="set-prompt-btn" class="set-prompt-btn">Set Prompt</button>
                </div>

                <div class="prompt-display" id="prompt-display" style="display: none;">
                    <div class="prompt-label">Using Custom Prompt:</div>
                    <div class="prompt-text" id="current-prompt-text"></div>
                    <button id="change-prompt-btn" class="change-prompt-btn">Change Prompt</button>
                </div>

                <div class="profiles-list-section">
                    <div class="profiles-list-header">Profile List:</div>
                    <div class="profiles-list-automation" id="profiles-list-automation"></div>
                </div>

                <div class="automation-controls">
                    <button id="start-automation-btn" class="start-automation-btn" disabled>🚀 Start Automation</button>
                    <button id="pause-automation-btn" class="pause-automation-btn" style="display: none;">⏸️ Pause</button>
                    <button id="stop-automation-btn" class="stop-automation-btn" style="display: none;">⏹️ Stop</button>
                </div>
            </div>
        `;

        this.setupAutomationEventListeners(automationUI);
        this.addAutomationStyles();

        return automationUI;
    }

    processConnectionRequests() {
        console.log('Processing connection requests for:', this.collectedProfiles);

        const sendRatio = this.config.stats?.sendConnectRatio || 0.7;
        const fieldRatio = this.config.stats?.fieldConnectRatio || 0.3;

        const sendConnectCount = this.ui.querySelector('#send-connect-count');
        const fieldConnectCount = this.ui.querySelector('#field-connect-count');

        sendConnectCount.textContent = Math.floor(this.collectedProfiles.length * sendRatio);
        fieldConnectCount.textContent = Math.floor(this.collectedProfiles.length * fieldRatio);
    }

    setupAutomationEventListeners(automationUI) {
        const closeBtn = automationUI.querySelector('.automation-close');
        const setPromptBtn = automationUI.querySelector('#set-prompt-btn');
        const changePromptBtn = automationUI.querySelector('#change-prompt-btn');
        const startAutomationBtn = automationUI.querySelector('#start-automation-btn');
        const pauseAutomationBtn = automationUI.querySelector('#pause-automation-btn');
        const stopAutomationBtn = automationUI.querySelector('#stop-automation-btn');
        const customPromptTextarea = automationUI.querySelector('#custom-prompt');

        closeBtn.addEventListener('click', () => {
            if (this.automationState.isRunning) {
                if (confirm('Automation is running. Are you sure you want to close?')) {
                    this.stopAutomation();
                    automationUI.remove();
                    if (this.ui) {
                        this.ui.style.display = 'flex';
                        this.ui.style.visibility = 'visible';
                    }
                }
            } else {
                automationUI.remove();
                if (this.ui) {
                    this.ui.style.display = 'flex';
                    this.ui.style.visibility = 'visible';
                }
            }
        });

        setPromptBtn.addEventListener('click', () => {
            const promptValue = customPromptTextarea.value.trim();
            if (!promptValue) {
                alert('Please enter a custom prompt!');
                return;
            }

            this.automationState.customPrompt = promptValue;
            this.automationState.promptSet = true;

            // Hide prompt input, show prompt display
            document.getElementById('prompt-section').style.display = 'none';
            document.getElementById('prompt-display').style.display = 'block';
            document.getElementById('current-prompt-text').textContent = promptValue;

            // Enable start button
            startAutomationBtn.disabled = false;
            startAutomationBtn.textContent = '🚀 Start Automation';
        });

        changePromptBtn.addEventListener('click', () => {
            this.automationState.promptSet = false;
            this.automationState.customPrompt = '';

            // Show prompt input, hide prompt display
            document.getElementById('prompt-section').style.display = 'block';
            document.getElementById('prompt-display').style.display = 'none';
            customPromptTextarea.value = '';

            // Disable start button
            startAutomationBtn.disabled = true;
            startAutomationBtn.textContent = '🚀 Start Automation (Set Prompt First)';
        });

        startAutomationBtn.addEventListener('click', () => {
            if (!this.automationState.promptSet || !this.automationState.customPrompt) {
                alert('Please set a custom prompt first!');
                return;
            }
            this.startAutomationProcess(automationUI);
        });

        pauseAutomationBtn.addEventListener('click', () => {
            this.pauseAutomation();
        });

        stopAutomationBtn.addEventListener('click', () => {
            if (confirm('Are you sure you want to stop the automation?')) {
                this.stopAutomation();
            }
        });
    }

    updateAutomationProgress() {
        const progressElement = document.querySelector('#automation-progress');
        const progressFillElement = document.querySelector('#progress-fill');
        const currentStatusElement = document.querySelector('#current-status');
        const currentProfileUrlElement = document.querySelector('#current-profile-url');
        const profilesListElement = document.querySelector('#profiles-list-automation');

        if (progressElement) {
            progressElement.textContent = `${this.automationState.currentProfileIndex} / ${this.automationState.totalProfiles}`;
        }

        if (progressFillElement) {
            const percentage = (this.automationState.currentProfileIndex / this.automationState.totalProfiles) * 100;
            progressFillElement.style.width = `${percentage}%`;
        }

        if (currentStatusElement) {
            const currentProfile = this.collectedProfiles[this.automationState.currentProfileIndex];
            if (currentProfile) {
                currentStatusElement.textContent = `Processing: ${currentProfile.name}`;
            }
        }

        if (currentProfileUrlElement) {
            const currentProfile = this.collectedProfiles[this.automationState.currentProfileIndex];
            if (currentProfile) {
                currentProfileUrlElement.textContent = currentProfile.url;
            }
        }

        if (profilesListElement) {
            this.updateAutomationProfilesList(profilesListElement);
        }

        // Update stats
        this.updateAutomationStats();
    }

    updateAutomationStats() {
        const successCount = document.querySelector('#success-count');
        const failedCount = document.querySelector('#failed-count');

        if (successCount && failedCount) {
            let successful = 0;
            let failed = 0;

            for (let i = 0; i < this.automationState.currentProfileIndex; i++) {
                const statusElement = document.getElementById(`profile-status-${i}`);
                if (statusElement) {
                    const status = statusElement.textContent.toLowerCase();
                    if (status.includes('connected') || status.includes('completed')) {
                        successful++;
                    } else if (status.includes('failed') || status.includes('error')) {
                        failed++;
                    }
                }
            }

            successCount.textContent = successful;
            failedCount.textContent = failed;
        }
    }

    pauseAutomation() {
        this.automationState.isRunning = false;

        const startBtn = document.querySelector('#start-automation-btn');
        const pauseBtn = document.querySelector('#pause-automation-btn');
        const stopBtn = document.querySelector('#stop-automation-btn');

        if (startBtn) {
            startBtn.style.display = 'inline-block';
            startBtn.textContent = '▶️ Resume Automation';
            startBtn.disabled = false;
        }
        if (pauseBtn) pauseBtn.style.display = 'none';
        if (stopBtn) stopBtn.style.display = 'inline-block';

        const currentStatusElement = document.querySelector('#current-status');
        if (currentStatusElement) {
            currentStatusElement.textContent = 'Automation paused. Click Resume to continue.';
        }
    }

    stopAutomation() {
        this.automationState.isRunning = false;
        this.automationState.currentProfileIndex = 0;

        const startBtn = document.querySelector('#start-automation-btn');
        const pauseBtn = document.querySelector('#pause-automation-btn');
        const stopBtn = document.querySelector('#stop-automation-btn');

        if (startBtn) {
            startBtn.style.display = 'inline-block';
            startBtn.textContent = '🚀 Start Automation';
            startBtn.disabled = !this.automationState.promptSet;
        }
        if (pauseBtn) pauseBtn.style.display = 'none';
        if (stopBtn) stopBtn.style.display = 'none';

        const currentStatusElement = document.querySelector('#current-status');
        if (currentStatusElement) {
            currentStatusElement.textContent = 'Automation stopped.';
        }
    }

    showPopupBlockerNotification() {
        // Remove any existing notification
        const existingNotification = document.querySelector('.popup-blocker-notification');
        if (existingNotification) {
            existingNotification.remove();
        }

        const notification = document.createElement('div');
        notification.className = 'popup-blocker-notification';
        notification.innerHTML = `
            <div style="
                position: fixed;
                top: 20px;
                right: 20px;
                background: #ff6b6b;
                color: white;
                padding: 16px 20px;
                border-radius: 8px;
                box-shadow: 0 4px 12px rgba(0,0,0,0.15);
                z-index: 10001;
                max-width: 350px;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            ">
                <div style="font-weight: 600; margin-bottom: 8px;">⚠️ Popup Blocked</div>
                <div style="font-size: 14px; margin-bottom: 12px;">
                    Please allow popups for LinkedIn to enable automatic profile opening.
                </div>
                <div style="font-size: 12px; color: #ffcccc;">
                    Click the popup blocker icon in your browser's address bar and select "Always allow popups from linkedin.com"
                </div>
                <button onclick="this.parentElement.parentElement.remove()" style="
                    position: absolute;
                    top: 8px;
                    right: 8px;
                    background: none;
                    border: none;
                    color: white;
                    font-size: 18px;
                    cursor: pointer;
                    width: 24px;
                    height: 24px;
                    border-radius: 50%;
                    display: flex;
                    align-items: center;
                    justify-content: center;
                ">&times;</button>
            </div>
        `;

        document.body.appendChild(notification);

        // Auto-remove after 10 seconds
        setTimeout(() => {
            if (notification.parentElement) {
                notification.remove();
            }
        }, 10000);
    }

    updateAutomationProfilesList(container) {
        container.innerHTML = this.collectedProfiles.map((profile, index) => {
            let status = 'Waiting';
            let statusClass = 'waiting';
            let statusIcon = '⏳';

            if (index < this.automationState.currentProfileIndex) {
                status = 'Completed';
                statusClass = 'completed';
                statusIcon = '✅';
            } else if (index === this.automationState.currentProfileIndex && this.automationState.isRunning) {
                status = 'Processing';
                statusClass = 'processing';
                statusIcon = '🔄';
            }

            return `
                <div class="automation-profile-item ${statusClass}" id="profile-item-${index}">
                    <div class="profile-avatar" id="profile-icon-${index}">${statusIcon}</div>
                    <div class="profile-info">
                        <div class="profile-name">${profile.name}</div>
                        <div class="profile-title">${profile.title}</div>
                        <div class="profile-company">${profile.company}</div>
                    </div>
                    <div class="profile-status" id="profile-status-${index}">${status}</div>
                </div>
            `;
        }).join('');
    }

    updateAutomationProfileStatus(index, status, icon, color) {
        const profileItem = document.getElementById(`profile-item-${index}`);
        const profileIcon = document.getElementById(`profile-icon-${index}`);
        const profileStatus = document.getElementById(`profile-status-${index}`);

        if (profileItem) {
            profileItem.className = `automation-profile-item ${status.toLowerCase()}`;
        }

        if (profileIcon) {
            profileIcon.textContent = icon;
            profileIcon.style.backgroundColor = color;
            profileIcon.style.color = 'white';
        }

        if (profileStatus) {
            profileStatus.textContent = status;
        }
    }

    clearProfiles() {
        if (this.isCollecting) {
            this.pauseCollecting();
        }
        this.collectedProfiles = [];
        this.updateProfileCount();
        this.updateProfilesList();
        const collectBtn = this.ui.querySelector('#collect-profiles-btn');
        const nextBtn = this.ui.querySelector('#start-connecting-btn');
        collectBtn.disabled = false;
        collectBtn.textContent = 'START COLLECTING';
        collectBtn.classList.remove('pause');
        collectBtn.classList.add('start');
        nextBtn.style.display = 'none';
        this.ui.querySelector('#send-connect-count').textContent = '0';
        this.ui.querySelector('#field-connect-count').textContent = '0';
        this.updateStatus('status', this.config.messages?.status?.ready || 'Ready to start collecting profiles', false);
    }

    async startAutomationProcess(automationUI) {
        this.automationState.isRunning = true;

        // Show notification about same-tab automation with popup preservation
        this.showNotification('Starting same-tab automation. The popup will be preserved during navigation.', 'info');

        // Update button states
        const startBtn = automationUI.querySelector('#start-automation-btn');
        const pauseBtn = automationUI.querySelector('#pause-automation-btn');
        const stopBtn = automationUI.querySelector('#stop-automation-btn');

        startBtn.style.display = 'none';
        pauseBtn.style.display = 'inline-block';
        stopBtn.style.display = 'inline-block';

        for (let i = this.automationState.currentProfileIndex; i < this.collectedProfiles.length; i++) {
            if (!this.automationState.isRunning) break; // Allow pausing/stopping

            this.automationState.currentProfileIndex = i;
            const profile = this.collectedProfiles[i];

            this.updateAutomationProgress();
            this.updateAutomationProfileStatus(i, 'Processing', '🔄', '#ffc107');

            try {
                // Step 1: Generate message from API
                const currentStatusElement = document.querySelector('#current-status');
                if (currentStatusElement) {
                    currentStatusElement.textContent = `Generating message for ${profile.name}`;
                }

                const messageData = await this.generateMessageForProfile(profile.url, this.automationState.customPrompt);

                if (!this.automationState.isRunning) break; // Check again after async operation

                // Step 2: Open profile and send connection request
                if (currentStatusElement) {
                    currentStatusElement.textContent = `Opening profile for ${profile.name}`;
                }

                const result = await this.openProfileAndConnect(profile.url, messageData.message, profile.name);

                if (!this.automationState.isRunning) break; // Check again after async operation

                // Step 3: Update status based on result
                if (result.success) {
                    this.updateAutomationProfileStatus(i, 'Connected', '✅', '#28a745');
                    if (currentStatusElement) {
                        currentStatusElement.textContent = `Successfully connected to ${profile.name}`;
                    }
                } else {
                    this.updateAutomationProfileStatus(i, 'Failed', '❌', '#dc3545');
                    if (currentStatusElement) {
                        currentStatusElement.textContent = `Failed to connect to ${profile.name}: ${result.error}`;
                    }

                    // Show popup blocker notification if needed
                    if (result.error && result.error.includes('Popup blocked')) {
                        this.showPopupBlockerNotification();
                    }
                }

                // Step 4: Wait before next profile (with interruption check)
                for (let wait = 0; wait < 3000; wait += 500) {
                    if (!this.automationState.isRunning) break;
                    await new Promise(resolve => setTimeout(resolve, 500));
                }

            } catch (error) {
                console.error(`Failed to process ${profile.name}:`, error);
                this.updateAutomationProfileStatus(i, 'Error', '⚠️', '#dc3545');
                const currentStatusElement = document.querySelector('#current-status');
                if (currentStatusElement) {
                    currentStatusElement.textContent = `Error processing ${profile.name}: ${error.message}`;
                }
            }
        }

        // Automation completed or stopped
        if (this.automationState.isRunning) {
            // Completed successfully
            this.automationState.isRunning = false;
            startBtn.textContent = 'Automation Completed ✓';
            startBtn.style.backgroundColor = '#28a745';
            startBtn.style.display = 'inline-block';
            pauseBtn.style.display = 'none';
            stopBtn.style.display = 'none';

            const currentStatusElement = document.querySelector('#current-status');
            if (currentStatusElement) {
                currentStatusElement.textContent = 'All profiles processed successfully!';
            }
        }
    }

    async generateMessageForProfile(profileUrl, customPrompt) {
        try {
            const response = await fetch('https://localhost:7007/api/linkedin/messages', {
                method: 'POST',
                headers: {
                    'accept': '*/*',
                    'Content-Type': 'application/json'
                },
                body: JSON.stringify({
                    url: profileUrl,
                    prompt: customPrompt
                })
            });

            if (!response.ok) {
                throw new Error(`API request failed: ${response.status}`);
            }

            const apiData = await response.json();
            console.log('API Data:', apiData);
            const message = apiData?.messages?.message1 || 'Hi, I\'m Ishu, a full-stack developer with expertise in .NET, Angular, and React. I\'d love to support your development needs. Let\'s connect and explore how I can add value to your team.';
            return { message };
        } catch (error) {
            console.error('API Service Error:', error);
            return { message: 'Hi, I\'m Ishu, a full-stack developer with expertise in .NET, Angular, and React. I\'d love to support your development needs. Let\'s connect and explore how I can add value to your team.' };
        }
    }

    async openProfileAndConnect(profileUrl, message, profileName) {
        return new Promise((resolve) => {
            // Store current page URL and automation context
            const currentUrl = window.location.href;

            // Store automation state for same-tab navigation
            const automationState = {
                currentUrl: currentUrl,
                profileUrl: profileUrl,
                message: message,
                profileName: profileName,
                timestamp: Date.now(),
                isAutomation: true,
                returnCallback: true
            };

            sessionStorage.setItem('linkedinAutomationState', JSON.stringify(automationState));

            // Store the resolve function reference for when we return
            window.automationResolve = resolve;

            // Navigate to profile in same tab
            window.location.href = profileUrl;
        });
    }

    updateStatus(statusType, message, isActive = false) {
        const statusText = this.ui.querySelector(`#${statusType}-text`);
        const statusDot = this.ui.querySelector(`#${statusType}-dot`);

        if (statusText) statusText.textContent = message;
        if (statusDot) {
            statusDot.classList.toggle('active', isActive);
        }
    }

    showNotification(message, type = 'info') {
        // Remove existing notifications
        const existingNotifications = document.querySelectorAll('.linkedin-automation-notification');
        existingNotifications.forEach(notification => notification.remove());

        const notification = document.createElement('div');
        notification.className = 'linkedin-automation-notification';
        notification.style.cssText = `
            position: fixed;
            top: 20px;
            right: 20px;
            background: ${type === 'error' ? '#dc3545' : type === 'success' ? '#28a745' : '#007bff'};
            color: white;
            padding: 15px 20px;
            border-radius: 8px;
            box-shadow: 0 4px 12px rgba(0,0,0,0.3);
            z-index: 10001;
            font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
            font-size: 14px;
            max-width: 350px;
            word-wrap: break-word;
        `;
        notification.textContent = message;

        document.body.appendChild(notification);

        // Auto-remove after 5 seconds for same-tab workflow
        setTimeout(() => {
            if (notification.parentNode) {
                notification.remove();
            }
        }, 5000);
    }

    addAutomationStyles() {
        if (document.querySelector('#automation-styles')) return;

        const style = document.createElement('style');
        style.id = 'automation-styles';
        style.textContent = `
            .automation-starter-ui {
                position: fixed;
                top: 50%;
                left: 50%;
                transform: translate(-50%, -50%);
                width: 650px;
                max-height: 85vh;
                background: white;
                border-radius: 12px;
                box-shadow: 0 20px 40px rgba(0,0,0,0.15);
                z-index: 10000;
                font-family: -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
                overflow: hidden;
            }

            .automation-header {
                background: linear-gradient(135deg, #0077b5, #005885);
                color: white;
                padding: 16px 20px;
                display: flex;
                justify-content: space-between;
                align-items: center;
            }

            .automation-header h3 {
                margin: 0;
                font-size: 18px;
                font-weight: 600;
            }

            .automation-close {
                background: none;
                border: none;
                color: white;
                font-size: 24px;
                cursor: pointer;
                padding: 0;
                width: 30px;
                height: 30px;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
            }

            .automation-close:hover {
                background: rgba(255,255,255,0.2);
            }

            .automation-content {
                padding: 20px;
                max-height: 65vh;
                overflow-y: auto;
            }

            .progress-section {
                margin-bottom: 16px;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #0077b5;
            }

            .progress-bar {
                width: 100%;
                height: 8px;
                background: #e9ecef;
                border-radius: 4px;
                margin-top: 8px;
                overflow: hidden;
            }

            .progress-fill {
                height: 100%;
                background: linear-gradient(90deg, #0077b5, #00a0dc);
                transition: width 0.3s ease;
            }

            .status-section {
                margin-bottom: 16px;
                padding: 12px;
                background: #e3f2fd;
                border-radius: 8px;
                border-left: 4px solid #2196f3;
            }

            .stats-section {
                display: flex;
                gap: 16px;
                margin-bottom: 16px;
                padding: 12px;
                background: #f8f9fa;
                border-radius: 8px;
                border-left: 4px solid #28a745;
            }

            .stat-item {
                flex: 1;
                text-align: center;
            }

            .stat-label {
                display: block;
                font-size: 12px;
                color: #666;
                margin-bottom: 4px;
            }

            .stat-value {
                display: block;
                font-size: 18px;
                font-weight: 600;
                color: #333;
            }

            .stat-value.success {
                color: #28a745;
            }

            .stat-value.failed {
                color: #dc3545;
            }

            .current-profile-section {
                margin-bottom: 16px;
                padding: 12px;
                background: #fff3cd;
                border-radius: 8px;
                border-left: 4px solid #ffc107;
            }

            .current-profile-label {
                font-weight: 600;
                margin-bottom: 4px;
                color: #333;
            }

            .current-profile-url {
                font-size: 12px;
                color: #666;
                word-break: break-all;
                background: white;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
            }

            .prompt-section, .prompt-display {
                margin-bottom: 16px;
                padding: 12px;
                background: #e8f5e8;
                border-radius: 8px;
                border-left: 4px solid #28a745;
            }

            .prompt-section label, .prompt-label {
                display: block;
                margin-bottom: 8px;
                font-weight: 600;
                color: #333;
            }

            .prompt-section textarea {
                width: 100%;
                padding: 12px;
                border: 2px solid #e1e5e9;
                border-radius: 8px;
                font-family: inherit;
                resize: vertical;
                margin-bottom: 12px;
                box-sizing: border-box;
            }

            .set-prompt-btn, .change-prompt-btn {
                background: #28a745;
                color: white;
                border: none;
                padding: 10px 20px;
                border-radius: 6px;
                cursor: pointer;
                font-weight: 600;
                font-size: 14px;
            }

            .change-prompt-btn {
                background: #6c757d;
                padding: 6px 12px;
                font-size: 12px;
            }

            .prompt-text {
                background: white;
                padding: 8px;
                border-radius: 4px;
                border: 1px solid #e9ecef;
                font-style: italic;
                color: #666;
                margin-bottom: 8px;
            }

            .profiles-list-section {
                margin-bottom: 20px;
            }

            .profiles-list-header {
                font-weight: 600;
                margin-bottom: 8px;
                color: #333;
            }

            .profiles-list-automation {
                max-height: 250px;
                overflow-y: auto;
                border: 1px solid #e1e5e9;
                border-radius: 8px;
                background: white;
            }

            .automation-profile-item {
                display: flex;
                align-items: center;
                padding: 12px;
                border-bottom: 1px solid #f0f0f0;
                transition: all 0.2s ease;
            }

            .automation-profile-item:last-child {
                border-bottom: none;
            }

            .automation-profile-item.waiting {
                background: #f8f9fa;
            }

            .automation-profile-item.processing {
                background: #fff3cd;
                border-left: 4px solid #ffc107;
                animation: pulse 2s infinite;
            }

            .automation-profile-item.connected {
                background: #d4edda;
                border-left: 4px solid #28a745;
            }

            .automation-profile-item.failed, .automation-profile-item.error {
                background: #f8d7da;
                border-left: 4px solid #dc3545;
            }

            .profile-avatar {
                width: 32px;
                height: 32px;
                background: #0077b5;
                border-radius: 50%;
                display: flex;
                align-items: center;
                justify-content: center;
                color: white;
                font-size: 14px;
                margin-right: 12px;
                flex-shrink: 0;
            }

            .profile-info {
                flex: 1;
                min-width: 0;
            }

            .profile-name {
                font-weight: 600;
                color: #333;
                margin-bottom: 2px;
                font-size: 14px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .profile-title {
                font-size: 12px;
                color: #666;
                margin-bottom: 2px;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .profile-company {
                font-size: 11px;
                color: #888;
                white-space: nowrap;
                overflow: hidden;
                text-overflow: ellipsis;
            }

            .profile-status {
                font-size: 11px;
                font-weight: 600;
                padding: 4px 8px;
                border-radius: 12px;
                text-transform: uppercase;
                flex-shrink: 0;
                min-width: 70px;
                text-align: center;
            }

            .automation-profile-item.waiting .profile-status {
                background: #e9ecef;
                color: #6c757d;
            }

            .automation-profile-item.processing .profile-status {
                background: #fff3cd;
                color: #856404;
            }

            .automation-profile-item.connected .profile-status {
                background: #d4edda;
                color: #155724;
            }

            .automation-profile-item.failed .profile-status,
            .automation-profile-item.error .profile-status {
                background: #f8d7da;
                color: #721c24;
            }

            .automation-controls {
                text-align: center;
                padding-top: 16px;
                border-top: 1px solid #e1e5e9;
                display: flex;
                gap: 12px;
                justify-content: center;
            }

            .start-automation-btn, .pause-automation-btn, .stop-automation-btn {
                background: linear-gradient(135deg, #0077b5, #005885);
                color: white;
                border: none;
                padding: 12px 24px;
                border-radius: 8px;
                font-size: 14px;
                font-weight: 600;
                cursor: pointer;
                transition: all 0.2s;
                flex: 1;
                max-width: 150px;
            }

            .pause-automation-btn {
                background: linear-gradient(135deg, #ffc107, #e0a800);
                color: #212529;
            }

            .stop-automation-btn {
                background: linear-gradient(135deg, #dc3545, #c82333);
            }

            .start-automation-btn:hover:not(:disabled),
            .pause-automation-btn:hover:not(:disabled),
            .stop-automation-btn:hover:not(:disabled) {
                transform: translateY(-2px);
                box-shadow: 0 4px 12px rgba(0,0,0,0.2);
            }

            .start-automation-btn:disabled {
                opacity: 0.7;
                cursor: not-allowed;
                transform: none;
            }

            @keyframes pulse {
                0% { opacity: 1; }
                50% { opacity: 0.7; }
                100% { opacity: 1; }
            }
        `;
        document.head.appendChild(style);
    }

    // Utility method to update any element text using configuration
    updateElementText(elementId, configPath, fallbackText = '') {
        const element = this.ui.querySelector(`#${elementId}`);
        if (element) {
            const configValue = this.getConfigValue(configPath);
            element.textContent = configValue || fallbackText;
        }
    }

    // Utility method to update button text using configuration
    updateButtonText(buttonId, configPath, fallbackText = '', suffix = '') {
        const button = this.ui.querySelector(`#${buttonId}`);
        if (button) {
            const configValue = this.getConfigValue(configPath);
            button.textContent = `${configValue || fallbackText}${suffix}`;
        }
    }

    showUI() {
        if (this.ui) {
            this.ui.style.display = 'flex';
            this.ui.style.visibility = 'visible';
            this.ui.style.opacity = '1';
        }
    }

    closeUI() {
        if (this.isCollecting) {
            this.pauseCollecting();
        }
        if (this.observer) {
            this.observer.disconnect();
            this.observer = null;
        }
        if (this.ui) {
            this.ui.remove();
        }
    }

    toggleMinimize() {
        const content = this.ui.querySelector('.linkedin-search-content');
        if (content.style.display === 'none') {
            content.style.display = 'block';
        } else {
            content.style.display = 'none';
        }
    }
}

// Handle profile automation when on profile page (same tab approach)
function handleProfileAutomation() {
    const automationState = sessionStorage.getItem('linkedinAutomationState');
    if (!automationState) return;

    try {
        const state = JSON.parse(automationState);
        if (!state.isAutomation) return;

        console.log('Starting profile automation for:', state.profileName);

        // Wait for page to load then start automation
        setTimeout(() => {
            performProfileConnection(state);
        }, 3000);

    } catch (error) {
        console.error('Error parsing automation state:', error);
        // Return to search page on error
        const state = JSON.parse(automationState);
        returnToSearchWithResult(state, {
            success: false,
            error: 'Failed to parse automation state'
        });
    }
}

// Perform connection on profile page (same tab approach)
async function performProfileConnection(state) {
    try {
        // Look for connect button
        const connectButton = findConnectButton();

        if (!connectButton) {
            // Return to search page with error
            returnToSearchWithResult(state, {
                success: false,
                error: 'Connect button not found on profile'
            });
            return;
        }

        // Click connect button
        connectButton.click();

        // Wait for popup and add note
        setTimeout(() => {
            addNoteAndSend(state);
        }, 2000);

    } catch (error) {
        console.error('Error in profile connection:', error);
        // Return to search page with error
        returnToSearchWithResult(state, {
            success: false,
            error: 'Error during profile connection: ' + error.message
        });
    }
}

// Find connect button on profile page
function findConnectButton() {
    const selectors = [
        'button[aria-label*="Connect"]',
        'button[data-control-name="connect"]',
        'button:contains("Connect")',
        '.pv-s-profile-actions button[aria-label*="Connect"]',
        '.pvs-profile-actions button[aria-label*="Connect"]'
    ];

    for (const selector of selectors) {
        const button = document.querySelector(selector);
        if (button && button.textContent.toLowerCase().includes('connect')) {
            return button;
        }
    }

    // Fallback: look for any button with "Connect" text
    const buttons = document.querySelectorAll('button');
    for (const button of buttons) {
        if (button.textContent.toLowerCase().includes('connect') &&
            !button.textContent.toLowerCase().includes('connected')) {
            return button;
        }
    }

    return null;
}

// Add note and send connection request (same tab approach)
function addNoteAndSend(state) {
    try {
        // Look for note textarea
        const noteTextarea = document.querySelector('textarea[name="message"]') ||
                           document.querySelector('textarea[aria-label*="message"]') ||
                           document.querySelector('.send-invite__custom-message textarea');

        if (noteTextarea) {
            // Add the message
            noteTextarea.value = state.message;
            noteTextarea.dispatchEvent(new Event('input', { bubbles: true }));
            noteTextarea.dispatchEvent(new Event('change', { bubbles: true }));
        }

        // Wait a bit then click send
        setTimeout(() => {
            const sendButton = document.querySelector('button[aria-label*="Send"]') ||
                             document.querySelector('button[data-control-name="send"]') ||
                             document.querySelector('.send-invite__actions button[aria-label*="Send"]');

            if (sendButton) {
                sendButton.click();

                // Wait for success then return to search
                setTimeout(() => {
                    returnToSearchWithResult(state, {
                        success: true,
                        message: 'Connection request sent successfully'
                    });
                }, 2000);
            } else {
                // No send button found, still consider it success if we got this far
                returnToSearchWithResult(state, {
                    success: true,
                    message: 'Connect button clicked, send button not found'
                });
            }
        }, 1000);

    } catch (error) {
        console.error('Error adding note and sending:', error);
        returnToSearchWithResult(state, {
            success: false,
            error: 'Error adding note: ' + error.message
        });
    }
}

// Helper function to return to search with result
function returnToSearchWithResult(state, result) {
    sessionStorage.removeItem('linkedinAutomationState');
    sessionStorage.setItem('automationResult', JSON.stringify(result));
    window.location.href = state.currentUrl;
}

// Auto-initialize when on LinkedIn search pages
function initLinkedInSearchUI() {
    console.log('initLinkedInSearchUI called, URL:', window.location.href);

    if (window.location.href.includes('linkedin.com/search') &&
        !document.querySelector('.linkedin-search-floating-ui') &&
        !document.querySelector('.automation-starter-ui') &&
        !window['linkedInSearchUI']) {

        console.log('Initializing LinkedIn Search UI...');

        // Check if we returned from profile automation first
        const automationResult = sessionStorage.getItem('automationResult');
        console.log('Automation result found:', !!automationResult);

        // Wait for page to load
        setTimeout(() => {
            console.log('Creating LinkedInSearchFloatingUI instance...');
            window['linkedInSearchUI'] = new LinkedInSearchFloatingUI();

            // Handle automation result if we returned from profile
            if (automationResult && window.automationResolve) {
                console.log('Processing automation result...');
                try {
                    const result = JSON.parse(automationResult);
                    sessionStorage.removeItem('automationResult');

                    // Resolve the promise from openProfileAndConnect
                    window.automationResolve(result);
                    window.automationResolve = null;

                    console.log('Automation result processed:', result);
                } catch (error) {
                    console.error('Error processing automation result:', error);
                    if (window.automationResolve) {
                        window.automationResolve({ success: false, error: 'Failed to process result' });
                        window.automationResolve = null;
                    }
                }
            }
        }, 2000);
    } else {
        console.log('LinkedIn Search UI not initialized - conditions not met');
        console.log('- Is search page:', window.location.href.includes('linkedin.com/search'));
        console.log('- No existing floating UI:', !document.querySelector('.linkedin-search-floating-ui'));
        console.log('- No existing starter UI:', !document.querySelector('.automation-starter-ui'));
        console.log('- No existing instance:', !window['linkedInSearchUI']);
    }
}

// Main initialization function
function initializeLinkedInAutomation() {
    if (window.location.href.includes('linkedin.com/search')) {
        // Initialize search UI
        initLinkedInSearchUI();
    } else if (window.location.href.includes('linkedin.com/in/')) {
        // Handle profile automation (new tab approach)
        handleProfileAutomation();
    }
}

// Initialize on page load
if (document.readyState === 'loading') {
    document.addEventListener('DOMContentLoaded', initializeLinkedInAutomation);
} else {
    initializeLinkedInAutomation();
}

// Re-initialize on navigation changes
let lastUrl = location.href;
new MutationObserver(() => {
    const url = location.href;
    if (url !== lastUrl) {
        lastUrl = url;
        setTimeout(initializeLinkedInAutomation, 1000);
    }
}).observe(document, { subtree: true, childList: true });

window.LinkedInSearchFloatingUI = LinkedInSearchFloatingUI;
